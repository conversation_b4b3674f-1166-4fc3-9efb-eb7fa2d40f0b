<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Roundels - HTML/CSS Version</title>
    <style>
        .roundels-container {
            display: flex;
            width: 309px;
            height: 124px;
            position: relative;
        }
        
        .roundel {
            border-radius: 62px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
        }
        
        .roundel-1 {
            width: 129px;
            height: 124px;
            background-image: url('roundel_1.jpg');
            z-index: 3;
        }
        
        .roundel-2 {
            width: 130px;
            height: 124px;
            margin-left: -40px;
            background-image: url('roundel_2.jpg');
            z-index: 2;
        }
        
        .roundel-3 {
            width: 130px;
            height: 124px;
            margin-left: -40px;
            background-image: url('roundel_3.jpg');
            z-index: 1;
        }
        
        /* Responsive design for mobile */
        @media (max-width: 768px) {
            .roundels-container {
                flex-direction: column;
                width: 130px;
                height: auto;
                align-items: center;
            }
            
            .roundel-2,
            .roundel-3 {
                margin-left: 0;
                margin-top: -20px;
            }
        }
        
        /* Alternative inline version for email compatibility */
        .roundels-inline {
            width: 309px;
            height: 124px;
            position: relative;
            display: inline-block;
        }
        
        .roundels-inline .roundel {
            position: absolute;
            border-radius: 62px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }
        
        .roundels-inline .roundel-1 {
            left: 0;
            top: 0;
            width: 129px;
            height: 124px;
            background-image: url('roundel_1.jpg');
            z-index: 3;
        }
        
        .roundels-inline .roundel-2 {
            left: 89px;
            top: 0;
            width: 130px;
            height: 124px;
            background-image: url('roundel_2.jpg');
            z-index: 2;
        }
        
        .roundels-inline .roundel-3 {
            left: 179px;
            top: 0;
            width: 130px;
            height: 124px;
            background-image: url('roundel_3.jpg');
            z-index: 1;
        }

        /* Roundels 2 - Second set (307x125) */
        .roundels2-container {
            display: flex;
            width: 307px;
            height: 125px;
            position: relative;
        }

        .roundel2 {
            border-radius: 62.5px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
        }

        .roundel2-1 {
            width: 129px;
            height: 125px;
            background-image: url('roundel_4.jpg');
            z-index: 3;
        }

        .roundel2-2 {
            width: 129px;
            height: 125px;
            margin-left: -40px;
            background-image: url('roundel_5.jpg');
            z-index: 2;
        }

        .roundel2-3 {
            width: 129px;
            height: 125px;
            margin-left: -40px;
            background-image: url('roundel_6.jpg');
            z-index: 1;
        }

        /* Alternative inline version for maximum email compatibility - Roundels 2 */
        .roundels2-inline {
            width: 307px;
            height: 125px;
            position: relative;
            display: inline-block;
        }

        .roundels2-inline .roundel2 {
            position: absolute;
            border-radius: 62.5px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
        }

        .roundels2-inline .roundel2-1 {
            left: 0;
            top: 0;
            width: 129px;
            height: 125px;
            background-image: url('roundel_4.jpg');
            z-index: 3;
        }

        .roundels2-inline .roundel2-2 {
            left: 89px;
            top: 0;
            width: 129px;
            height: 125px;
            background-image: url('roundel_5.jpg');
            z-index: 2;
        }

        .roundels2-inline .roundel2-3 {
            left: 178px;
            top: 0;
            width: 129px;
            height: 125px;
            background-image: url('roundel_6.jpg');
            z-index: 1;
        }
    </style>
</head>
<body>
    <h2>Flexbox Version (Recommended)</h2>
    <div class="roundels-container">
        <div class="roundel roundel-1"></div>
        <div class="roundel roundel-2"></div>
        <div class="roundel roundel-3"></div>
    </div>
    
    <h2>Absolute Positioning Version (Email Compatible)</h2>
    <div class="roundels-inline">
        <div class="roundel roundel-1"></div>
        <div class="roundel roundel-2"></div>
        <div class="roundel roundel-3"></div>
    </div>

    <h2>Roundels 2 - Flexbox Version</h2>
    <div class="roundels2-container">
        <div class="roundel2 roundel2-1"></div>
        <div class="roundel2 roundel2-2"></div>
        <div class="roundel2 roundel2-3"></div>
    </div>

    <h2>Roundels 2 - Absolute Positioning Version</h2>
    <div class="roundels2-inline">
        <div class="roundel2 roundel2-1"></div>
        <div class="roundel2 roundel2-2"></div>
        <div class="roundel2 roundel2-3"></div>
    </div>
</body>
</html>
