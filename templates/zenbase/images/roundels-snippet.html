<!-- Roundels HTML/CSS Implementation -->
<!-- Replace the SVG with this HTML snippet -->

<!-- Option 1: Flexbox version (recommended for modern browsers) -->
<div class="roundels-container">
    <div class="roundel roundel-1"></div>
    <div class="roundel roundel-2"></div>
    <div class="roundel roundel-3"></div>
</div>

<!-- Option 2: Absolute positioning (better email compatibility) -->
<div class="roundels-inline">
    <div class="roundel roundel-1"></div>
    <div class="roundel roundel-2"></div>
    <div class="roundel roundel-3"></div>
</div>

<!-- Option 3: Fallback with actual img tags (maximum compatibility) -->
<div class="roundels-fallback">
    <img src="roundel_1.jpg" alt="Roundel 1" width="129" height="124">
    <img src="roundel_2.jpg" alt="Roundel 2" width="130" height="124">
    <img src="roundel_3.jpg" alt="Roundel 3" width="130" height="124">
</div>

<!-- ROUNDELS 2 (Second Set) -->

<!-- Option 1: Flexbox version (recommended for modern browsers) -->
<div class="roundels2-container">
    <div class="roundel2 roundel2-1"></div>
    <div class="roundel2 roundel2-2"></div>
    <div class="roundel2 roundel2-3"></div>
</div>

<!-- Option 2: Absolute positioning (better email compatibility) -->
<div class="roundels2-inline">
    <div class="roundel2 roundel2-1"></div>
    <div class="roundel2 roundel2-2"></div>
    <div class="roundel2 roundel2-3"></div>
</div>

<!-- Option 3: Fallback with actual img tags (maximum compatibility) -->
<div class="roundels2-fallback">
    <img src="roundel_4.jpg" alt="Roundel 4" width="129" height="125">
    <img src="roundel_5.jpg" alt="Roundel 5" width="129" height="125">
    <img src="roundel_6.jpg" alt="Roundel 6" width="129" height="125">
</div>
